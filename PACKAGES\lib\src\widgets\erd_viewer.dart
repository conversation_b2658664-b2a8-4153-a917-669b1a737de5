import 'package:flutter/material.dart';
import 'package:graphview/GraphView.dart';
import '../models/erd_models.dart';
import 'entity_card.dart';

/// A widget that displays an Entity-Relationship Diagram with interactive features
class ERDViewer extends StatelessWidget {
  /// List of entities to display in the diagram
  final List<Entity> entities;

  /// Width of each entity card
  final double nodeWidth;

  /// Height of each entity card
  final double nodeHeight;

  /// Spacing between nodes
  final double nodeSpacing;

  /// Color for relationship lines
  final Color primaryColor;

  /// Background color of the diagram
  final Color backgroundColor;

  /// Minimum zoom level
  final double minScale;

  /// Maximum zoom level
  final double maxScale;

  const ERDViewer({
    super.key,
    required this.entities,
    this.nodeWidth = 250,
    this.nodeHeight = 120,
    this.nodeSpacing = 150,
    this.primaryColor = Colors.blue,
    this.backgroundColor = Colors.white,
    this.minScale = 0.1,
    this.maxScale = 2.0,
  });

  @override
  Widget build(BuildContext context) {
    // Create graph
    final graph = Graph()..isTree = true;

    // Create nodes and edges
    final nodeMap = <String, Node>{};
    
    // Add nodes
    for (final entity in entities) {
      final node = Node.Id(entity.name);
      nodeMap[entity.name] = node;
      graph.addNode(node);
    }

    // Add edges based on foreign key relationships
    for (final entity in entities) {
      for (final attribute in entity.attributes) {
        if (attribute.isForeignKey && attribute.referencesEntity != null) {
          final fromNode = nodeMap[entity.name];
          final toNode = nodeMap[attribute.referencesEntity];
          
          if (fromNode != null && toNode != null) {
            graph.addEdge(fromNode, toNode);
          }
        }
      }
    }

    // Create algorithm for tree layout
    final algorithm = BuchheimWalkerConfiguration(
      builder: BuchheimWalkerBuilder()
        ..siblingSeparation = nodeSpacing
        ..levelSeparation = nodeSpacing
        ..subtreeSeparation = nodeSpacing
        ..orientation = BuchheimWalkerConfiguration.ORIENTATION_TOP_BOTTOM,
    );

    return Container(
      color: backgroundColor,
      child: InteractiveViewer(
        constrained: false,
        boundaryMargin: const EdgeInsets.all(100),
        minScale: minScale,
        maxScale: maxScale,
        child: GraphView(
          graph: graph,
          algorithm: algorithm,
          paint: Paint()
            ..color = primaryColor
            ..strokeWidth = 2
            ..style = PaintingStyle.stroke,
          builder: (Node node) {
            // Find the entity for this node
            final entity = entities.firstWhere(
              (e) => e.name == node.key?.value,
              orElse: () => throw Exception('Entity not found for node ${node.key?.value}'),
            );

            return SizedBox(
              width: nodeWidth,
              height: nodeHeight,
              child: EntityCard(entity: entity),
            );
          },
        ),
      ),
    );
  }
} 