# Flutter ERD

A Flutter package for visualizing Entity-Relationship Diagrams (ERDs) from Dart entity definitions.

## Features

- Parse Dart entity classes to extract entity and relationship information
- Parse JSON data to create entity definitions
- Visualize entities and their relationships in a clean, modern diagram
- Customizable appearance (colors, sizes, text styles)
- Support for various relationship types (one-to-one, one-to-many, many-to-many)

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  flutter_erd: ^0.1.0
```

## Usage

### Using JSON Input

You can define your entities using JSON format:

```dart
import 'package:flutter_erd/flutter_erd.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final jsonString = '''
    [
      {
        "name": "User",
        "fields": [
          {
            "name": "id",
            "type": "int",
            "isPrimaryKey": true
          },
          {
            "name": "name",
            "type": "String"
          },
          {
            "name": "email",
            "type": "String",
            "isUnique": true
          }
        ],
        "relationships": [
          {
            "fromEntity": "User",
            "toEntity": "Post",
            "type": "one-to-many",
            "foreignKey": "authorId"
          }
        ]
      },
      {
        "name": "Post",
        "fields": [
          {
            "name": "id",
            "type": "int",
            "isPrimaryKey": true
          },
          {
            "name": "title",
            "type": "String"
          },
          {
            "name": "content",
            "type": "String"
          }
        ],
        "relationships": [
          {
            "fromEntity": "Post",
            "toEntity": "User",
            "type": "many-to-one",
            "foreignKey": "authorId"
          }
        ]
      }
    ]
    ''';

    final entities = JsonEntityParser.parseString(jsonString);

    return Scaffold(
      body: ERDDiagram(
        entities: entities,
        primaryColor: Colors.blue,
        backgroundColor: Colors.white,
      ),
    );
  }
}
```

### Using Dart Classes

You can also define your entities using Dart classes:

```dart
class User {
  @PrimaryKey
  final int id;
  final String name;
  final String email;
  
  @OneToMany('Post')
  final List<Post> posts;
}

class Post {
  @PrimaryKey
  final int id;
  final String title;
  final String content;
  
  @ManyToOne('User')
  final User author;
}
```

## JSON Format

The JSON format for entity definitions follows this structure:

```json
[
  {
    "name": "EntityName",
    "fields": [
      {
        "name": "fieldName",
        "type": "fieldType",
        "isPrimaryKey": false,
        "isNullable": false,
        "isUnique": false
      }
    ],
    "relationships": [
      {
        "fromEntity": "EntityName",
        "toEntity": "RelatedEntityName",
        "type": "one-to-one|one-to-many|many-to-many",
        "foreignKey": "optionalForeignKey"
      }
    ]
  }
]
```

## Customization

The `ERDDiagram` widget supports various customization options:

```dart
ERDDiagram(
  entities: entities,
  nodeWidth: 200,  // Width of entity boxes
  nodeHeight: 40,  // Height of entity boxes
  nodeSpacing: 100,  // Space between nodes
  primaryColor: Colors.blue,  // Color of lines and borders
  backgroundColor: Colors.white,  // Background color
  textStyle: TextStyle(fontSize: 16),  // Text style for labels
)
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details. 