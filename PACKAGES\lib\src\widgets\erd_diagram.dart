import 'package:flutter/material.dart';
import 'package:graphview/GraphView.dart';
import '../models/erd_models.dart';
import 'entity_card.dart';

/// A widget that displays an Entity-Relationship Diagram using graphview
class ERDDiagram extends StatelessWidget {
  final List<Entity> entities;
  final double nodeWidth;
  final double nodeHeight;
  final double nodeSpacing;
  final Color primaryColor;
  final Color backgroundColor;

  const ERDDiagram({
    super.key,
    required this.entities,
    this.nodeWidth = 250,
    this.nodeHeight = 120,
    this.nodeSpacing = 150,
    this.primaryColor = Colors.blue,
    this.backgroundColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    // Create graph and algorithm
    final graph = Graph()..isTree = true;
    final algorithm = BuchheimWalkerConfiguration(
      siblingSeparation: nodeSpacing,
      levelSeparation: nodeSpacing,
      subtreeSeparation: nodeSpacing,
      orientation: BuchheimWalkerConfiguration.ORIENTATION_TOP_BOTTOM,
    );

    // Create nodes and edges
    final nodeMap = <String, Node>{};
    
    // Add nodes
    for (final entity in entities) {
      final node = Node.Id(entity.name);
      nodeMap[entity.name] = node;
      graph.addNode(node);
    }

    // Add edges based on foreign key relationships
    for (final entity in entities) {
      for (final attribute in entity.attributes) {
        if (attribute.isForeignKey && attribute.referencesEntity != null) {
          final fromNode = nodeMap[entity.name];
          final toNode = nodeMap[attribute.referencesEntity];
          
          if (fromNode != null && toNode != null) {
            graph.addEdge(fromNode, toNode);
          }
        }
      }
    }

    return InteractiveViewer(
      constrained: false,
      boundaryMargin: const EdgeInsets.all(100),
      minScale: 0.1,
      maxScale: 2.0,
      child: GraphView(
        graph: graph,
        algorithm: algorithm,
        paint: Paint()
          ..color = primaryColor
          ..strokeWidth = 2
          ..style = PaintingStyle.stroke,
        builder: (Node node) {
          // Find the entity for this node
          final entity = entities.firstWhere(
            (e) => e.name == node.key?.value,
            orElse: () => throw Exception('Entity not found for node ${node.key?.value}'),
          );

          return SizedBox(
            width: nodeWidth,
            height: nodeHeight,
            child: EntityCard(entity: entity),
          );
        },
      ),
    );
  }
} 