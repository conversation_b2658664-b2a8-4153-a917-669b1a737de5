import 'dart:convert';
import 'package:erd_viewer/erd_viewer.dart';
import 'package:flutter_erd/src/models/entity.dart';

/// Parser for converting JSON data into Entity objects
class JsonEntityParser {
  /// Parse a JSON string into a list of Entity objects
  static List<Entity> parseString(String jsonString) {
    try {
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => _parseEntity(json)).toList();
    } catch (e) {
      throw FormatException('Invalid JSON format: $e');
    }
  }

  /// Parse a JSON map into an Entity object
  static Entity _parseEntity(Map<String, dynamic> json) {
    final fields = <EntityField>[];
    final relationships = <EntityRelationship>[];

    // Parse fields
    if (json['fields'] != null) {
      for (final fieldJson in json['fields']) {
        fields.add(_parseField(fieldJson));
      }
    }

    // Parse relationships
    if (json['relationships'] != null) {
      for (final relJson in json['relationships']) {
        relationships.add(_parseRelationship(relJson));
      }
    }

    return Entity(
      name: json['name'] as String,
      fields: fields,
      relationships: relationships,
    );
  }

  /// Parse a JSON map into an EntityField object
  static EntityField _parseField(Map<String, dynamic> json) {
    return EntityField(
      name: json['name'] as String,
      type: json['type'] as String,
      isPrimaryKey: json['isPrimaryKey'] as bool? ?? false,
      isNullable: json['isNullable'] as bool? ?? false,
      isUnique: json['isUnique'] as bool? ?? false,
    );
  }

  /// Parse a JSON map into an EntityRelationship object
  static EntityRelationship _parseRelationship(Map<String, dynamic> json) {
    return EntityRelationship(
      fromEntity: json['fromEntity'] as String,
      toEntity: json['toEntity'] as String,
      type: json['type'] as String,
      foreignKey: json['foreignKey'] as String?,
    );
  }
} 