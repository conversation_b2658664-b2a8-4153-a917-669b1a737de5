import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Represents an attribute/field in an entity
class Attribute {
  final String name;
  final String type;
  final bool isPrimaryKey;
  final bool isForeignKey;
  final String? referencesEntity;

  const Attribute({
    required this.name,
    required this.type,
    this.isPrimaryKey = false,
    this.isForeignKey = false,
    this.referencesEntity,
  });

  /// Convert Attribute to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'isPrimaryKey': isPrimaryKey,
      'isForeignKey': isForeignKey,
      if (referencesEntity != null) 'referencesEntity': referencesEntity,
    };
  }

  /// Create Attribute from JSON
  factory Attribute.fromJson(Map<String, dynamic> json) {
    return Attribute(
      name: json['name'] as String,
      type: json['type'] as String,
      isPrimaryKey: json['isPrimaryKey'] as bool? ?? false,
      isForeignKey: json['isForeignKey'] as bool? ?? false,
      referencesEntity: json['referencesEntity'] as String?,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Attribute &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          type == other.type &&
          isPrimaryKey == other.isPrimaryKey &&
          isForeignKey == other.isForeignKey &&
          referencesEntity == other.referencesEntity;

  @override
  int get hashCode => Object.hash(
        name,
        type,
        isPrimaryKey,
        isForeignKey,
        referencesEntity,
      );
}

/// Represents an entity in the ERD
class Entity {
  final String name;
  final List<Attribute> attributes;
  final List<Map<String, dynamic>> data;

  const Entity({
    required this.name,
    required this.attributes,
    this.data = const [],
  });

  /// Convert Entity to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'attributes': attributes.map((attr) => attr.toJson()).toList(),
      if (data.isNotEmpty) 'data': data,
    };
  }

  /// Create Entity from JSON
  factory Entity.fromJson(Map<String, dynamic> json) {
    return Entity(
      name: json['name'] as String,
      attributes: (json['attributes'] as List)
          .map((attr) => Attribute.fromJson(attr as Map<String, dynamic>))
          .toList(),
      data: (json['data'] as List?)?.map((row) => Map<String, dynamic>.from(row as Map)).toList() ?? [],
    );
  }

  /// Convert Entity to JSON string
  String toJsonString() => jsonEncode(toJson());

  /// Create Entity from JSON string
  factory Entity.fromJsonString(String jsonString) {
    return Entity.fromJson(jsonDecode(jsonString) as Map<String, dynamic>);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Entity &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          attributes == other.attributes &&
          data == other.data;

  @override
  int get hashCode => Object.hash(name, attributes, data);
} 