import 'package:flutter/material.dart';
import 'package:erd_viewer/erd_viewer.dart';
import 'dart:math';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ERD Viewer Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const ERDDraggableDemo(),
    );
  }
}

class ERDTable {
  final String id;
  final Entity entity;
  Offset position;
  ERDTable({required this.id, required this.entity, required this.position});
}

class ERDRelation {
  final String from;
  final String to;
  final String fromCardinality; // e.g. "1", "N"
  final String toCardinality;   // e.g. "1", "N"
  ERDRelation({
    required this.from,
    required this.to,
    required this.fromCardinality,
    required this.toCardinality,
  });
}

class ERDDraggableDemo extends StatefulWidget {
  const ERDDraggableDemo({super.key});

  @override
  State<ERDDraggableDemo> createState() => _ERDDraggableDemoState();
}

class _ERDDraggableDemoState extends State<ERDDraggableDemo> {
  static const double cardWidth = 220;
  static const double cardHeight = 200;
  static const double gridSize = 40.0;

  late List<ERDTable> tables;
  late List<ERDRelation> relations;
  Entity? selectedEntity;
  Offset? selectedEntityPosition;
  String? highlightedAttribute;
  bool showDataCard = false;
  double _zoomLevel = 1.0; // Initial zoom level
  static const double _minZoom = 0.5;
  static const double _maxZoom = 2.0;

  @override
  void initState() {
    super.initState();
    // Initial positions (can be loaded from storage)
    tables = [
      ERDTable(
        id: 'order_items',
        entity: Entity(
          name: 'order_items',
          attributes: [
            Attribute(name: 'order_id', type: 'int', isForeignKey: true, referencesEntity: 'orders'),
            Attribute(name: 'product_id', type: 'int', isForeignKey: true, referencesEntity: 'products'),
            Attribute(name: 'quantity', type: 'int'),
          ],
          data: [
            {'order_id': 1, 'product_id': 101, 'quantity': 2},
            {'order_id': 1, 'product_id': 102, 'quantity': 1},
            {'order_id': 2, 'product_id': 101, 'quantity': 3},
            {'order_id': 2, 'product_id': 102, 'quantity': 2},
            {'order_id': 3, 'product_id': 101, 'quantity': 1},
            {'order_id': 3, 'product_id': 103, 'quantity': 4},
            {'order_id': 4, 'product_id': 104, 'quantity': 2},
            {'order_id': 5, 'product_id': 105, 'quantity': 1},
            {'order_id': 6, 'product_id': 106, 'quantity': 2},
            {'order_id': 7, 'product_id': 107, 'quantity': 3},
          ],
        ),
        position: const Offset(40, 40),
      ),
      ERDTable(
        id: 'products',
        entity: Entity(
          name: 'products',
          attributes: [
            Attribute(name: 'id', type: 'int', isPrimaryKey: true),
            Attribute(name: 'name', type: 'varchar'),
            Attribute(name: 'merchant_id', type: 'int', isForeignKey: true, referencesEntity: 'merchants'),
            Attribute(name: 'price', type: 'int'),
            Attribute(name: 'status', type: 'varchar'),
            Attribute(name: 'created_at', type: 'varchar'),
            Attribute(name: 'category_id', type: 'int', isForeignKey: true, referencesEntity: 'categories'),
          ],
          data: [
            {'id': 101, 'name': 'Widget', 'merchant_id': 201, 'price': 25, 'status': 'active', 'created_at': '2024-01-01', 'category_id': 301},
            {'id': 102, 'name': 'Gadget', 'merchant_id': 202, 'price': 40, 'status': 'inactive', 'created_at': '2024-01-02', 'category_id': 302},
            {'id': 103, 'name': 'Thingamajig', 'merchant_id': 201, 'price': 15, 'status': 'active', 'created_at': '2024-01-03', 'category_id': 301},
            {'id': 104, 'name': 'Doodad', 'merchant_id': 202, 'price': 30, 'status': 'active', 'created_at': '2024-01-04', 'category_id': 302},
            {'id': 105, 'name': 'Gizmo', 'merchant_id': 201, 'price': 50, 'status': 'inactive', 'created_at': '2024-01-05', 'category_id': 301},
            {'id': 106, 'name': 'Contraption', 'merchant_id': 202, 'price': 60, 'status': 'active', 'created_at': '2024-01-06', 'category_id': 302},
            {'id': 107, 'name': 'Device', 'merchant_id': 201, 'price': 20, 'status': 'active', 'created_at': '2024-01-07', 'category_id': 301},
            {'id': 108, 'name': 'Apparatus', 'merchant_id': 202, 'price': 35, 'status': 'inactive', 'created_at': '2024-01-08', 'category_id': 302},
            {'id': 109, 'name': 'Instrument', 'merchant_id': 201, 'price': 45, 'status': 'active', 'created_at': '2024-01-09', 'category_id': 301},
            {'id': 110, 'name': 'Tool', 'merchant_id': 202, 'price': 55, 'status': 'active', 'created_at': '2024-01-10', 'category_id': 302},
          ],
        ),
        position: const Offset(40, 320),
      ),
      ERDTable(
        id: 'orders',
        entity: Entity(
          name: 'orders',
          attributes: [
            Attribute(name: 'id', type: 'int', isPrimaryKey: true),
            Attribute(name: 'user_id', type: 'int', isForeignKey: true, referencesEntity: 'users'),
            Attribute(name: 'status', type: 'varchar'),
            Attribute(name: 'created_at', type: 'varchar'),
          ],
          data: [
            {'id': 1, 'user_id': 501, 'status': 'pending', 'created_at': '2024-01-01'},
            {'id': 2, 'user_id': 502, 'status': 'shipped', 'created_at': '2024-01-03'},
            {'id': 3, 'user_id': 501, 'status': 'delivered', 'created_at': '2024-01-05'},
            {'id': 4, 'user_id': 502, 'status': 'pending', 'created_at': '2024-01-07'},
            {'id': 5, 'user_id': 501, 'status': 'shipped', 'created_at': '2024-01-09'},
            {'id': 6, 'user_id': 502, 'status': 'delivered', 'created_at': '2024-01-11'},
            {'id': 7, 'user_id': 501, 'status': 'pending', 'created_at': '2024-01-13'},
            {'id': 8, 'user_id': 502, 'status': 'shipped', 'created_at': '2024-01-15'},
            {'id': 9, 'user_id': 501, 'status': 'delivered', 'created_at': '2024-01-17'},
            {'id': 10, 'user_id': 502, 'status': 'pending', 'created_at': '2024-01-19'},
          ],
        ),
        position: const Offset(340, 40),
      ),
      ERDTable(
        id: 'merchants',
        entity: Entity(
          name: 'merchants',
          attributes: [
            Attribute(name: 'id', type: 'int', isPrimaryKey: true),
            Attribute(name: 'admin_id', type: 'int'),
            Attribute(name: 'merchant_name', type: 'varchar'),
            Attribute(name: 'country_code', type: 'varchar', isForeignKey: true, referencesEntity: 'countries'),
            Attribute(name: 'created_at', type: 'varchar'),
          ],
          data: [
            {'id': 201, 'admin_id': 1, 'merchant_name': 'Acme', 'country_code': 'US', 'created_at': '2024-01-01'},
            {'id': 202, 'admin_id': 2, 'merchant_name': 'Globex', 'country_code': 'UK', 'created_at': '2024-01-02'},
            {'id': 203, 'admin_id': 3, 'merchant_name': 'Umbrella', 'country_code': 'US', 'created_at': '2024-01-03'},
            {'id': 204, 'admin_id': 4, 'merchant_name': 'Wayne', 'country_code': 'UK', 'created_at': '2024-01-04'},
            {'id': 205, 'admin_id': 5, 'merchant_name': 'Stark', 'country_code': 'US', 'created_at': '2024-01-05'},
            {'id': 206, 'admin_id': 6, 'merchant_name': 'Oscorp', 'country_code': 'UK', 'created_at': '2024-01-06'},
            {'id': 207, 'admin_id': 7, 'merchant_name': 'LexCorp', 'country_code': 'US', 'created_at': '2024-01-07'},
            {'id': 208, 'admin_id': 8, 'merchant_name': 'Initech', 'country_code': 'UK', 'created_at': '2024-01-08'},
            {'id': 209, 'admin_id': 9, 'merchant_name': 'Hooli', 'country_code': 'US', 'created_at': '2024-01-09'},
            {'id': 210, 'admin_id': 10, 'merchant_name': 'Vandelay', 'country_code': 'UK', 'created_at': '2024-01-10'},
          ],
        ),
        position: const Offset(340, 320),
      ),
      ERDTable(
        id: 'categories',
        entity: Entity(
          name: 'categories',
          attributes: [
            Attribute(name: 'id', type: 'int', isPrimaryKey: true),
            Attribute(name: 'name', type: 'varchar'),
            Attribute(name: 'parent_id', type: 'int', isForeignKey: true, referencesEntity: 'categories'),
          ],
          data: [
            {'id': 301, 'name': 'Electronics', 'parent_id': null},
            {'id': 302, 'name': 'Accessories', 'parent_id': 301},
            {'id': 303, 'name': 'Home', 'parent_id': null},
            {'id': 304, 'name': 'Garden', 'parent_id': 303},
            {'id': 305, 'name': 'Toys', 'parent_id': null},
            {'id': 306, 'name': 'Games', 'parent_id': 305},
            {'id': 307, 'name': 'Books', 'parent_id': null},
            {'id': 308, 'name': 'Comics', 'parent_id': 307},
            {'id': 309, 'name': 'Clothing', 'parent_id': null},
            {'id': 310, 'name': 'Shoes', 'parent_id': 309},
          ],
        ),
        position: const Offset(640, 320),
      ),
      ERDTable(
        id: 'users',
        entity: Entity(
          name: 'users',
          attributes: [
            Attribute(name: 'id', type: 'int', isPrimaryKey: true),
            Attribute(name: 'full_name', type: 'varchar'),
            Attribute(name: 'email', type: 'varchar'),
            Attribute(name: 'gender', type: 'varchar'),
            Attribute(name: 'date_of_birth', type: 'date'),
            Attribute(name: 'created_at', type: 'varchar'),
            Attribute(name: 'country_code', type: 'varchar', isForeignKey: true, referencesEntity: 'countries'),
          ],
          data: [
            {'id': 501, 'full_name': 'Alice Smith', 'email': '<EMAIL>', 'gender': 'F', 'date_of_birth': '1990-01-01', 'created_at': '2024-01-01', 'country_code': 'US'},
            {'id': 502, 'full_name': 'Bob Jones', 'email': '<EMAIL>', 'gender': 'M', 'date_of_birth': '1985-05-12', 'created_at': '2024-01-02', 'country_code': 'UK'},
            {'id': 503, 'full_name': 'Carol White', 'email': '<EMAIL>', 'gender': 'F', 'date_of_birth': '1992-03-15', 'created_at': '2024-01-03', 'country_code': 'US'},
            {'id': 504, 'full_name': 'David Black', 'email': '<EMAIL>', 'gender': 'M', 'date_of_birth': '1988-07-22', 'created_at': '2024-01-04', 'country_code': 'UK'},
            {'id': 505, 'full_name': 'Eve Green', 'email': '<EMAIL>', 'gender': 'F', 'date_of_birth': '1995-11-30', 'created_at': '2024-01-05', 'country_code': 'US'},
            {'id': 506, 'full_name': 'Frank Blue', 'email': '<EMAIL>', 'gender': 'M', 'date_of_birth': '1983-09-10', 'created_at': '2024-01-06', 'country_code': 'UK'},
            {'id': 507, 'full_name': 'Grace Red', 'email': '<EMAIL>', 'gender': 'F', 'date_of_birth': '1991-05-18', 'created_at': '2024-01-07', 'country_code': 'US'},
            {'id': 508, 'full_name': 'Hank Yellow', 'email': '<EMAIL>', 'gender': 'M', 'date_of_birth': '1987-12-25', 'created_at': '2024-01-08', 'country_code': 'UK'},
            {'id': 509, 'full_name': 'Ivy Purple', 'email': '<EMAIL>', 'gender': 'F', 'date_of_birth': '1993-04-02', 'created_at': '2024-01-09', 'country_code': 'US'},
            {'id': 510, 'full_name': 'Jack Orange', 'email': '<EMAIL>', 'gender': 'M', 'date_of_birth': '1989-08-14', 'created_at': '2024-01-10', 'country_code': 'UK'},
          ],
        ),
        position: const Offset(940, 40),
      ),
      ERDTable(
        id: 'countries',
        entity: Entity(
          name: 'countries',
          attributes: [
            Attribute(name: 'code', type: 'varchar', isPrimaryKey: true),
            Attribute(name: 'name', type: 'varchar'),
            Attribute(name: 'continent_name', type: 'varchar'),
          ],
          data: [
            {'code': 'US', 'name': 'United States', 'continent_name': 'North America'},
            {'code': 'UK', 'name': 'United Kingdom', 'continent_name': 'Europe'},
            {'code': 'CA', 'name': 'Canada', 'continent_name': 'North America'},
            {'code': 'FR', 'name': 'France', 'continent_name': 'Europe'},
            {'code': 'DE', 'name': 'Germany', 'continent_name': 'Europe'},
            {'code': 'JP', 'name': 'Japan', 'continent_name': 'Asia'},
            {'code': 'CN', 'name': 'China', 'continent_name': 'Asia'},
            {'code': 'IN', 'name': 'India', 'continent_name': 'Asia'},
            {'code': 'BR', 'name': 'Brazil', 'continent_name': 'South America'},
            {'code': 'AU', 'name': 'Australia', 'continent_name': 'Oceania'},
          ],
        ),
        position: const Offset(1240, 40),
      ),
    ];
    relations = [
      ERDRelation(from: 'order_items', to: 'orders', fromCardinality: 'N', toCardinality: '1'),
      ERDRelation(from: 'order_items', to: 'products', fromCardinality: 'N', toCardinality: '1'),
      ERDRelation(from: 'orders', to: 'users', fromCardinality: 'N', toCardinality: '1'),
      ERDRelation(from: 'products', to: 'merchants', fromCardinality: 'N', toCardinality: '1'),
      ERDRelation(from: 'products', to: 'categories', fromCardinality: 'N', toCardinality: '1'),
      ERDRelation(from: 'merchants', to: 'countries', fromCardinality: 'N', toCardinality: '1'),
      ERDRelation(from: 'users', to: 'countries', fromCardinality: 'N', toCardinality: '1'),
    ];
  }

  Offset snapToGrid(Offset rawPosition, double gridSize) {
    final x = (rawPosition.dx / gridSize).round() * gridSize;
    final y = (rawPosition.dy / gridSize).round() * gridSize;
    return Offset(x, y);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ERD Draggable Demo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Stack(
        children: [
          Center(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: Transform.scale(
                  scale: _zoomLevel,
                  child: SizedBox(
                    width: 2400,
                    height: 1400,
                    child: Stack(
                      children: [
                        // Edges
                        CustomPaint(
                          size: const Size(2400, 1400),
                          painter: _ERDEdgePainter(
                            tables: tables,
                            relations: relations,
                            cardWidth: cardWidth,
                            cardHeight: cardHeight,
                          ),
                        ),
                        // Draggable tables
                        for (final table in tables)
                          Positioned(
                            left: table.position.dx,
                            top: table.position.dy,
                            child: GestureDetector(
                              onPanUpdate: (details) {
                                setState(() {
                                  table.position += details.delta / _zoomLevel; // Adjust for zoom
                                  if (selectedEntity == table.entity) {
                                    selectedEntityPosition = table.position;
                                  }
                                });
                              },
                              onPanEnd: (details) {
                                setState(() {
                                  table.position = snapToGrid(table.position, gridSize);
                                  if (selectedEntity == table.entity) {
                                    selectedEntityPosition = table.position;
                                  }
                                });
                              },
                              child: SizedBox(
                                width: cardWidth,
                                child: EntityCard(
                                  entity: table.entity,
                                  onEntitySelected: () {
                                    setState(() {
                                      selectedEntity = table.entity;
                                      selectedEntityPosition = table.position;
                                      // Just select the entity without showing data card
                                      showDataCard = false;
                                      highlightedAttribute = null;
                                    });
                                  },
                                  onInfoTap: () {
                                    setState(() {
                                      selectedEntity = table.entity;
                                      selectedEntityPosition = table.position;
                                      // Show data card when info icon is clicked
                                      showDataCard = true;
                                      highlightedAttribute = null;
                                    });
                                  },
                                ),
                              ),
                            ),
                          ),
                        // Crow's foot legend box
                        Positioned(
                          top: 24,
                          right: 24,
                          child: CrowFootLegendBox(),
                        ),
                        // Data card for selected entity, positioned next to the entity
                        if (selectedEntity != null && selectedEntityPosition != null && showDataCard)
                          Builder(
                            builder: (context) {
                              // Default: to the right of the entity
                              double left = selectedEntityPosition!.dx + cardWidth + 24;
                              double top = selectedEntityPosition!.dy;
                              const double dataCardWidth = 420;
                              const double dataCardHeight = 320;
                              // If it would overflow right, show to the left
                              if (left + dataCardWidth > 2400) {
                                left = selectedEntityPosition!.dx - dataCardWidth - 24;
                              }
                              // If it would overflow bottom, adjust up
                              if (top + dataCardHeight > 1400) {
                                top = 1400 - dataCardHeight - 24;
                              }
                              // If it would overflow top, adjust down
                              if (top < 0) top = 24;
                              return Positioned(
                                left: left,
                                top: top,
                                child: _EntityDataCard(
                                  entity: selectedEntity!,
                                  highlightedAttribute: highlightedAttribute,
                                  onClose: () {
                                    setState(() {
                                      selectedEntity = null;
                                      selectedEntityPosition = null;
                                      highlightedAttribute = null;
                                    });
                                  },
                                ),
                              );
                            },
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Zoom controls on the right side
          Positioned(
            right: 24,
            top: MediaQuery.of(context).size.height / 2 - 80,
            child: _ZoomControl(
              minZoom: _minZoom,
              maxZoom: _maxZoom,
              zoomLevel: _zoomLevel,
              onZoomChanged: (newZoom) {
                setState(() {
                  _zoomLevel = newZoom;
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Zoom control widget that shows + and - buttons with a slider
class _ZoomControl extends StatelessWidget {
  final double minZoom;
  final double maxZoom;
  final double zoomLevel;
  final ValueChanged<double> onZoomChanged;

  const _ZoomControl({
    Key? key,
    required this.minZoom,
    required this.maxZoom,
    required this.zoomLevel,
    required this.onZoomChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Zoom in button (plus sign)
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.black, width: 1.5),
            ),
            child: Center(
              child: Icon(Icons.add, size: 20),
            ),
          ).asButton(() {
            final newZoom = (zoomLevel + 0.1).clamp(minZoom, maxZoom);
            onZoomChanged(newZoom);
          }),
          
          // Vertical line with handle
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 2,
            height: 100,
            color: Colors.black,
            alignment: Alignment.center,
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                // Green circular draggable handle
                Positioned(
                  left: -9,
                  // Position based on current zoom level (reversed because up is higher zoom)
                  top: 100 * (1 - (zoomLevel - minZoom) / (maxZoom - minZoom)) - 9,
                  child: GestureDetector(
                    onVerticalDragUpdate: (details) {
                      final lineHeight = 100;
                      // Reverse the calculation (down = more zoom, up = less zoom)
                      final yPos = 1.0 - (details.localPosition.dy / lineHeight)
                          .clamp(0.0, 1.0);
                      final newZoom = minZoom + yPos * (maxZoom - minZoom);
                      onZoomChanged(newZoom);
                    },
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.lightGreen.shade200,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Zoom out button (minus sign)
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.black, width: 1.5),
            ),
            child: Center(
              child: Icon(Icons.remove, size: 20),
            ),
          ).asButton(() {
            final newZoom = (zoomLevel - 0.1).clamp(minZoom, maxZoom);
            onZoomChanged(newZoom);
          }),
        ],
      ),
    );
  }
}

// Extension method to make a widget tappable
extension WidgetExtension on Widget {
  Widget asButton(VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: this,
    );
  }
}

class _ERDEdgePainter extends CustomPainter {
  final List<ERDTable> tables;
  final List<ERDRelation> relations;
  final double cardWidth;
  final double cardHeight;

  static const double headerHeight = 48;
  static const double attributeRowHeight = 32;
  static const double verticalPadding = 12;

  _ERDEdgePainter({
    required this.tables,
    required this.relations,
    required this.cardWidth,
    required this.cardHeight,
  });

  double getCardHeight(Entity entity) {
    return headerHeight + entity.attributes.length * attributeRowHeight + 2 * verticalPadding;
  }

  // Find the Y offset for a given attribute name in an entity
  double getAttributeYOffset(Entity entity, String attributeName) {
    final idx = entity.attributes.indexWhere((a) => a.name == attributeName);
    if (idx == -1) return headerHeight + verticalPadding + attributeRowHeight / 2; // fallback to first row
    return headerHeight + verticalPadding + idx * attributeRowHeight + attributeRowHeight / 2;
  }

  // Find the first PK attribute name in an entity
  String? getPrimaryKeyAttribute(Entity entity) {
    final pk = entity.attributes.firstWhere((a) => a.isPrimaryKey, orElse: () => entity.attributes.first);
    return pk.name;
  }

  // Helper to get the connection point on the closest edge
  Offset getConnectionPoint(ERDTable table, double yOffset, Offset target, bool isSource) {
    final left = Offset(table.position.dx, table.position.dy + yOffset);
    final right = Offset(table.position.dx + cardWidth, table.position.dy + yOffset);
    final top = Offset(table.position.dx + cardWidth / 2, table.position.dy);
    final bottom = Offset(table.position.dx + cardWidth / 2, table.position.dy + getCardHeight(table.entity));
    // Compute distances
    final points = [left, right, top, bottom];
    points.sort((a, b) => (a - target).distance.compareTo((b - target).distance));
    // Prefer horizontal edges for source, vertical for target
    if (isSource) {
      if ((right - target).distance < (left - target).distance) return right;
      return left;
    } else {
      if ((left - target).distance < (right - target).distance) return left;
      return right;
    }
  }

  void drawCrowsFoot(Canvas canvas, Offset point, double angle, {double size = 14}) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;
    // Angles for the three prongs
    for (final offsetAngle in [-0.5, 0.0, 0.5]) {
      final dx = size * cos(angle + offsetAngle);
      final dy = size * sin(angle + offsetAngle);
      canvas.drawLine(point, point + Offset(dx, dy), paint);
    }
  }

  void drawOne(Canvas canvas, Offset point, double angle, {double size = 10}) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;
    // Perpendicular to the line
    final dx = size * cos(angle + pi / 2);
    final dy = size * sin(angle + pi / 2);
    canvas.drawLine(point - Offset(dx / 2, dy / 2), point + Offset(dx / 2, dy / 2), paint);
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFB0B0B0)
      ..strokeWidth = 2;
    final tableMap = {for (var t in tables) t.id: t};
    for (final rel in relations) {
      // Only process self-referencing once
      if (rel.from == rel.to) {
        final from = tableMap[rel.from]!;
        final fromEntity = from.entity;
        final fkAttr = fromEntity.attributes.firstWhere(
          (a) => a.isForeignKey && a.referencesEntity == fromEntity.name,
          orElse: () => fromEntity.attributes.first,
        );
        final fromYOffset = getAttributeYOffset(fromEntity, fkAttr.name);
        // Draw a curved self-loop on the right side
        final start = Offset(from.position.dx + cardWidth, from.position.dy + fromYOffset);
        final cp1 = start + const Offset(40, -40);
        final cp2 = start + const Offset(40, 40);
        final end = start + const Offset(0, 60);
        final path = Path()
          ..moveTo(start.dx, start.dy)
          ..cubicTo(cp1.dx, cp1.dy, cp2.dx, cp2.dy, end.dx, end.dy);
        canvas.drawPath(path, paint);
        // Draw only one notation, offset from the curve (from side only)
        final labelPos = start + const Offset(32, 0);
        final textPainter = TextPainter(
          text: TextSpan(
            text: rel.fromCardinality,
            style: const TextStyle(color: Colors.black, fontSize: 14, fontWeight: FontWeight.bold),
          ),
          textDirection: TextDirection.ltr,
        )..layout();
        // Draw white background
        final bgRect = Rect.fromCenter(
          center: labelPos,
          width: textPainter.width + 8,
          height: textPainter.height + 4,
        );
        final rrect = RRect.fromRectAndRadius(bgRect, const Radius.circular(6));
        canvas.drawRRect(rrect, Paint()..color = Colors.white);
        textPainter.paint(canvas, labelPos - Offset(textPainter.width / 2, textPainter.height / 2));
        continue; // Do not draw a regular edge or extra notation
      }
      final from = tableMap[rel.from]!;
      final to = tableMap[rel.to]!;
      final fromEntity = from.entity;
      final toEntity = to.entity;
      // Find the FK attribute in the source entity that references the target
      final fkAttr = fromEntity.attributes.firstWhere(
        (a) => a.isForeignKey && a.referencesEntity == toEntity.name,
        orElse: () => fromEntity.attributes.first,
      );
      // Find the PK attribute in the target entity
      final pkAttrName = getPrimaryKeyAttribute(toEntity) ?? toEntity.attributes.first.name;

      final fromYOffset = getAttributeYOffset(fromEntity, fkAttr.name);
      final toYOffset = getAttributeYOffset(toEntity, pkAttrName);

      // For non-self-referencing, connect closest edges
      final fromPoint = getConnectionPoint(from, fromYOffset, to.position, true);
      final toPoint = getConnectionPoint(to, toYOffset, from.position, false);
      canvas.drawLine(fromPoint, toPoint, paint);

      // Draw crow's foot or one notation at each end
      final dx = toPoint.dx - fromPoint.dx;
      final dy = toPoint.dy - fromPoint.dy;
      final angle = atan2(dy, dx);
      // Offset a bit away from the end points
      const double symbolOffset = 8;
      if (rel.fromCardinality == 'N') {
        drawCrowsFoot(canvas, fromPoint + Offset(cos(angle) * symbolOffset, sin(angle) * symbolOffset), angle);
      } else if (rel.fromCardinality == '1') {
        drawOne(canvas, fromPoint + Offset(cos(angle) * symbolOffset, sin(angle) * symbolOffset), angle);
      }
      if (rel.toCardinality == 'N') {
        drawCrowsFoot(canvas, toPoint - Offset(cos(angle) * symbolOffset, sin(angle) * symbolOffset), angle + pi);
      } else if (rel.toCardinality == '1') {
        drawOne(canvas, toPoint - Offset(cos(angle) * symbolOffset, sin(angle) * symbolOffset), angle + pi);
      }

      // Remove cardinality text notations (N, 1) and their backgrounds
      // (No textPainterFrom, textPainterTo, or white backgrounds)
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Crow's foot legend widget
class CrowFootLegendBox extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Crow\'s Foot Notation', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            _LegendRow(symbol: _OneSymbol(), label: 'One (|)'),
            _LegendRow(symbol: _CrowsFootSymbol(), label: 'Many (crow\'s foot)'),
            // Add more for zero/one, zero/many if you support them
          ],
        ),
      ),
    );
  }
}

class _LegendRow extends StatelessWidget {
  final Widget symbol;
  final String label;
  const _LegendRow({required this.symbol, required this.label});
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(width: 28, height: 18, child: symbol),
          const SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }
}

class _OneSymbol extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _OneSymbolPainter(),
    );
  }
}

class _OneSymbolPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;
    canvas.drawLine(Offset(size.width / 2, 2), Offset(size.width / 2, size.height - 2), paint);
  }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _CrowsFootSymbol extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _CrowsFootSymbolPainter(),
    );
  }
}

class _CrowsFootSymbolPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;
    final center = Offset(size.width / 2, size.height / 2);
    for (final offsetAngle in [-0.5, 0.0, 0.5]) {
      final dx = 10 * cos(offsetAngle);
      final dy = 10 * sin(offsetAngle);
      canvas.drawLine(center, center + Offset(dx, dy), paint);
    }
  }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _EntityDataCard extends StatelessWidget {
  final Entity entity;
  final String? highlightedAttribute;
  final VoidCallback onClose;

  const _EntityDataCard({
    Key? key,
    required this.entity,
    this.highlightedAttribute,
    required this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${entity.name} Data',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onClose,
                ),
              ],
            ),
            const SizedBox(height: 16),
            entity.data.isEmpty
                ? const Text('No data available.')
                : SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      columns: [
                        for (final attr in entity.attributes)
                          DataColumn(
                            label: Text(
                              attr.name,
                              style: TextStyle(
                                fontWeight: highlightedAttribute == attr.name ? FontWeight.bold : FontWeight.normal,
                                color: highlightedAttribute == attr.name ? Colors.blue : null,
                              ),
                            ),
                          ),
                      ],
                      rows: [
                        for (final row in entity.data)
                          DataRow(
                            cells: [
                              for (final attr in entity.attributes)
                                DataCell(Text(row[attr.name]?.toString() ?? '')),
                            ],
                          ),
                      ],
                    ),
                  ),
          ],
        ),
      ),
    );
  }
} 