import 'package:analyzer/dart/ast/ast.dart';
import 'package:analyzer/dart/ast/visitor.dart';
import 'package:flutter_erd/src/models/entity.dart';

import '../models/entity.dart';

/// A visitor that extracts entity information from Dart code
class EntityVisitor extends RecursiveAstVisitor<void> {
  final List<Entity> entities = [];

  @override
  void visitClassDeclaration(ClassDeclaration node) {
    final entity = _parseEntity(node);
    if (entity != null) {
      entities.add(entity);
    }
    super.visitClassDeclaration(node);
  }

  Entity? _parseEntity(ClassDeclaration node) {
    final fields = <EntityField>[];
    final relationships = <EntityRelationship>[];

    for (final member in node.members) {
      if (member is FieldDeclaration) {
        for (final variable in member.fields.variables) {
          final type = member.fields.type;
          if (type != null) {
            final field = _parseField(variable, type);
            if (field != null) {
              fields.add(field);
            }
          }
        }
      }
    }

    if (fields.isEmpty) return null;

    return Entity(
      name: node.name.lexeme,
      fields: fields,
      relationships: relationships,
    );
  }

  EntityField? _parseField(VariableDeclaration variable, TypeAnnotation type) {
    final annotations = variable.metadata;
    bool isPrimaryKey = false;
    bool isNullable = false;
    bool isUnique = false;

    for (final annotation in annotations) {
      final name = annotation.name.name;
      if (name == 'PrimaryKey') {
        isPrimaryKey = true;
      } else if (name == 'Unique') {
        isUnique = true;
      }
    }

    // Check if the type is nullable
    if (type is NamedType && type.question != null) {
      isNullable = true;
    }

    return EntityField(
      name: variable.name.lexeme,
      type: type.toString(),
      isPrimaryKey: isPrimaryKey,
      isNullable: isNullable,
      isUnique: isUnique,
    );
  }
}

/// Parser for extracting entity information from Dart code
class EntityParser {
  /// Parse a Dart file and extract entity information
  static List<Entity> parseFile(String filePath) {
    // TODO: Implement file parsing using analyzer package
    // This is a placeholder implementation
    return [];
  }

  /// Parse a Dart string and extract entity information
  static List<Entity> parseString(String code) {
    // TODO: Implement string parsing using analyzer package
    // This is a placeholder implementation
    return [];
  }
} 