import 'package:flutter/material.dart';
import '../models/entity.dart';

/// A widget that displays an Entity in a card format, styled like the sample ERD image
typedef AttributeLinkTapCallback = void Function(Attribute attribute, {String? referencedEntity});

class EntityCard extends StatelessWidget {
  final Entity entity;
  final VoidCallback? onAttributeTap;
  final VoidCallback? onEntitySelected;
  final VoidCallback? onInfoTap;

  const EntityCard({
    super.key,
    required this.entity,
    this.onAttributeTap,
    this.onEntitySelected,
    this.onInfoTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onEntitySelected,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Blue header with info icon
            Container(
              decoration: const BoxDecoration(
                color: Color(0xFF2176C1),
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      entity.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                  // Info icon button
                  if (onInfoTap != null)
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: (){ 
                          onInfoTap!();
                        },
                        customBorder: const CircleBorder(),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.info_outline,
                            color: Colors.white,
                            size: 18,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Attribute list
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (int i = 0; i < entity.attributes.length; i++) ...[
                    InkWell(
                      onTap: onAttributeTap,
                      child: Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Text(
                                  entity.attributes[i].name,
                                  style: TextStyle(
                                    fontWeight: entity.attributes[i].isPrimaryKey ? FontWeight.bold : FontWeight.normal,
                                    color: Colors.black87,
                                  ),
                                ),
                                if (entity.attributes[i].isPrimaryKey)
                                  const Padding(
                                    padding: EdgeInsets.only(left: 4),
                                    child: Text(
                                      '(PK)',
                                      style: TextStyle(fontSize: 11, color: Color(0xFF1976D2)),
                                    ),
                                  ),
                                if (entity.attributes[i].isForeignKey)
                                  const Padding(
                                    padding: EdgeInsets.only(left: 4),
                                    child: Text(
                                      '(FK)',
                                      style: TextStyle(fontSize: 11, color: Color(0xFF43A047)),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            entity.attributes[i].type,
                            style: const TextStyle(color: Colors.black54),
                          ),
                        ],
                      ),
                    ),
                    if (i < entity.attributes.length - 1)
                      const Divider(height: 16, thickness: 0.7, color: Color(0xFFE0E0E0)),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _KeyBadge extends StatelessWidget {
  final String label;
  final Color color;
  const _KeyBadge({required this.label, required this.color});
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 2),
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.12),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color, width: 1),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 11,
          letterSpacing: 0.5,
        ),
      ),
    );
  }
}

// Dialog to show entity data
class _EntityDataDialog extends StatelessWidget {
  final Entity entity;
  const _EntityDataDialog({required this.entity});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('${entity.name} Data'),
      content: SizedBox(
        width: 400,
        child: entity.data.isEmpty
            ? const Text('No data available.')
            : SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  columns: [
                    for (final attr in entity.attributes)
                      DataColumn(label: Text(attr.name)),
                  ],
                  rows: [
                    for (final row in entity.data)
                      DataRow(
                        cells: [
                          for (final attr in entity.attributes)
                            DataCell(Text(row[attr.name]?.toString() ?? '')),
                        ],
                      ),
                  ],
                ),
              ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
} 